"""
Document Question Extractor Service

This service handles the extraction of questions from document PDFs.
It converts PDFs to images, extracts text, parses questions using LLM,
and saves the results to the database.
"""

import logging
import os
import json
import math
import uuid
import asyncio
import tempfile
import traceback

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, qp_parser_prompt, pre_process_prompt
from services.mcq_text_extractor_service import MCQTextExtractorService
from services.request_tracker_service import RequestTrackerService
from utils.s3_utils import upload_file_to_s3, read_file_from_s3, get_s3_path
from db_config.pyqs_admin_db import create_exam_solution
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage
from llm_manager.prompts import pre_process_explanation_prompt
import ast

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)


class DocumentQuestionExtractorService:
    """
    Service for extracting questions from document PDFs.
    """

    def __init__(self, max_concurrent_extractions=None):
        """
        Initialize the DocumentQuestionExtractorService.
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1.5)
        self.question_batch_size = getattr(config, 'MCQ_BATCH_SIZE', 10)
        
        # Delay LLM initialization until first use
        self.llm = None
        self._llm_initialized = False
        
        # Initialize request tracker
        self.tracker_service = RequestTrackerService()

    def _initialize_llm(self):
        """Initialize LLM if not already initialized."""
        if not self._llm_initialized:
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True

    async def extract_questions_from_document(self, document_id, question_paper_path, total_questions, username=None):
        """
        Extract questions from a document PDF.
        """
        # Create a task for tracking
        task_id = self.tracker_service.create_task("document_question_extraction")

        if not task_id:
            return {"status": "error", "message": "Failed to create tracking task"}

        # Start the extraction process in background - use asyncio.create_task to ensure it runs independently
        # This will return immediately without waiting for the background task to complete
        background_task = asyncio.create_task(self._extract_questions_background(
            task_id, document_id, question_paper_path, total_questions, username
        ))

        # Don't await the background task - let it run independently
        logger.info(f"[TASK:{task_id}] Background extraction task created for document {document_id}")

        return {"status": "success", "task_id": task_id}

    async def _extract_questions_background(self, task_id, document_id, question_paper_path, total_questions, username=None):
        """
        Background task for extracting questions from document.
        """
        try:
            logger.info(f"[TASK:{task_id}] Starting document question extraction for document ID {document_id}")
            
            # Update task status to in progress
            self.tracker_service.update_task_status(task_id, "IN_PROGRESS")
            
            # Step 1: Convert PDF to images
            logger.info(f"[TASK:{task_id}] Step 1: Converting PDF to images")

            
            try:
                # Convert PDF to images
                pdf_converter = PDFImageConverter()
                conversion_result = pdf_converter.convert_and_upload(
                    pdf_path=question_paper_path,
                    book_id="documents",
                    chapter_id=str(document_id),
                    res_id="main",
                    zoom=self.pdf_zoom_factor
                )
                
                if conversion_result["status"] != "success":
                    raise Exception(f"PDF conversion failed: {conversion_result['message']}")
                
                # Get column image URLs
                col_img_urls = conversion_result.get("cropped_image_urls", [])
                if not col_img_urls:
                    raise Exception("No column images were generated")

                logger.info(f"[TASK:{task_id}] Found {len(col_img_urls)} column images")

                # Check the local storage directory (but don't fail if there are issues)
                local_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, "documents", str(document_id), "main")
                if os.path.exists(local_storage_path):
                    try:
                        all_files = os.listdir(local_storage_path)
                        png_files = [f for f in all_files if f.endswith('.png')]
                        logger.info(f"[TASK:{task_id}] Local storage directory contains {len(all_files)} total files, {len(png_files)} PNG files")
                    except Exception as e:
                        logger.warning(f"[TASK:{task_id}] Error accessing local storage directory {local_storage_path}: {e}")
                else:
                    logger.warning(f"[TASK:{task_id}] Local storage directory does not exist: {local_storage_path}")
                    logger.warning(f"[TASK:{task_id}] Continuing with text extraction, some files may fail")
                
                # Step 2: Extract text from images in parallel
                logger.info(f"[TASK:{task_id}] Step 2: Extracting text from images")

                # Check if the local page images directory exists
                local_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, "documents", str(document_id), "main")
                if os.path.exists(local_storage_path):
                    try:
                        files_in_dir = os.listdir(local_storage_path)
                        png_files = [f for f in files_in_dir if f.endswith('.png')]
                        logger.info(f"[TASK:{task_id}] Found {len(files_in_dir)} files, {len(png_files)} PNG files in {local_storage_path}")
                    except Exception as e:
                        logger.warning(f"[TASK:{task_id}] Error listing files in {local_storage_path}: {e}")
                else:
                    logger.warning(f"[TASK:{task_id}] Local page images directory does not exist: {local_storage_path}")

                text_extractor = MCQTextExtractorService()
                text_files = await text_extractor._extract_text_parallel(
                    col_img_urls, task_id, "main", str(document_id), "documents"
                )

                # Continue even if some text files are missing - we'll handle this in the merge step
                logger.info(f"[TASK:{task_id}] Text extraction completed, received {len(text_files) if text_files else 0} text files")

                # Step 3: Merge all text files into one combined file
                logger.info(f"[TASK:{task_id}] Step 3: Merging text files")
                combined_file_path = await text_extractor._merge_text_files(
                    text_files, str(document_id), "main", task_id
                )

                if not combined_file_path:
                    # If no combined file was created, check if we have any valid text files at all
                    if not text_files or len([f for f in text_files if f and os.path.exists(f)]) == 0:
                        raise Exception("No valid text files were created for merging")
                    else:
                        raise Exception("Failed to create combined text file despite having valid text files")
                
                # Step 4: Upload combined file to S3
                logger.info(f"[TASK:{task_id}] Step 4: Uploading combined text to S3")
                s3_upload_result = upload_file_to_s3(
                    local_file_path=combined_file_path,
                    book_id="documents",
                    chapter_id=str(document_id),
                    res_id="main",
                    file_name=f"{document_id}_questions.txt",
                    is_quiz_image=False
                )

                if not s3_upload_result:
                    raise Exception(f"Failed to upload combined text to S3")

                full_s3_path = s3_upload_result
                
                # Step 5: Process question parsing in batches
                logger.info(f"[TASK:{task_id}] Step 5: Processing question parsing")
                questions = await self._process_question_parsing_batches(
                    full_s3_path, total_questions, document_id, task_id, combined_file_path
                )
                
                if not questions:
                    raise Exception("No questions were extracted")

                # Step 6: Extract MCQ images from page images
                logger.info(f"[TASK:{task_id}] Step 6: Extracting MCQ images from page images")
                images_from_content = self._extract_quiz_images(
                    "main", str(document_id), "documents",
                    conversion_result.get("image_urls", []),
                    task_id
                )

                # Map images to questions (simple approach)
                if images_from_content:
                    logger.info(f"[TASK:{task_id}] Appending {len(images_from_content)} images to questions")
                    questions = self._append_images_to_questions(questions, images_from_content, task_id)
                else:
                    logger.info(f"[TASK:{task_id}] No images extracted from content")

                # Step 7: Save questions to database
                logger.info(f"[TASK:{task_id}] Step 7: Saving questions to database")
                saved_count = await self._save_questions_to_database(questions, document_id, username, task_id)
                
                # Update task status to completed
                result_data = {
                    "document_id": document_id,
                    "total_questions_extracted": len(questions),
                    "total_questions_saved": saved_count,
                    "s3_text_path": full_s3_path
                }
                
                self.tracker_service.update_task_status(
                    task_id, "COMPLETED", 
                    result_data=json.dumps(result_data)
                )
                
                logger.info(f"[TASK:{task_id}] Document question extraction completed successfully")

                # Enhancement 2: Delete local folder after completion
                logger.info(f"[TASK:{task_id}] Step 7: Cleaning up local files")
                await self._cleanup_local_folder(document_id, task_id)

                # Clean up text files after successful processing
                try:
                    if text_files:
                        for text_file in text_files:
                            if os.path.exists(text_file):
                                os.unlink(text_file)
                                logger.debug(f"[TASK:{task_id}] Cleaned up text file: {text_file}")

                    if combined_file_path and os.path.exists(combined_file_path):
                        os.unlink(combined_file_path)
                        logger.debug(f"[TASK:{task_id}] Cleaned up combined file: {combined_file_path}")
                except Exception as cleanup_error:
                    logger.warning(f"[TASK:{task_id}] Error during cleanup: {cleanup_error}")

            except Exception as inner_e:
                # Clean up files on error as well
                try:
                    if 'text_files' in locals() and text_files:
                        for text_file in text_files:
                            if os.path.exists(text_file):
                                os.unlink(text_file)

                    if 'combined_file_path' in locals() and combined_file_path and os.path.exists(combined_file_path):
                        os.unlink(combined_file_path)

                    # Also cleanup local folder on error
                    await self._cleanup_local_folder(document_id, task_id)
                except:
                    pass
                raise inner_e
                
        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in document question extraction: {e}")
            logger.error(traceback.format_exc())
            
            # Update task status to failed
            self.tracker_service.update_task_status(task_id, "FAILED", error_message=str(e))

    async def _process_question_parsing_batches(self, s3_text_path, total_questions, document_id, task_id, combined_file_path):
        """Process question parsing in batches and combine results."""
        try:
            # Use configurable batch size
            batch_size = self.question_batch_size
            num_batches = math.ceil(total_questions / batch_size)

            logger.info(f"[TASK:{task_id}] Processing {total_questions} questions in {num_batches} batches of {batch_size}")

            # Read text content from S3
            full_s3_path = get_s3_path(s3_text_path)
            content = read_file_from_s3(full_s3_path)
            if content is None:
                logger.error(f"[TASK:{task_id}] Failed to read text content from S3: {s3_text_path}")
                return []

            # Convert bytes to string
            text_content = content.decode("utf-8")

            # Create output directory for JSON files
            json_output_dir = os.path.join(os.path.dirname(combined_file_path), "json_batches")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []
            failed_batches = []

            # Process each batch
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[TASK:{task_id}] Processing batch {batch_num}/{num_batches}: questions {start_question}-{end_question}")

                # Call LLM for this batch
                batch_json_content = await self._call_llm_for_batch(
                    text_content, start_question, end_question, task_id
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"batch_{batch_num}_{start_question}_{end_question}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        json.dump(batch_json_content, f, indent=2, ensure_ascii=False)

                    batch_json_files.append(batch_json_path)
                    logger.info(f"[TASK:{task_id}] Batch {batch_num} completed and saved")
                else:
                    failed_batches.append(batch_num)
                    logger.error(f"[TASK:{task_id}] Batch {batch_num} failed")

            if failed_batches:
                logger.warning(f"[TASK:{task_id}] {len(failed_batches)} batches failed: {failed_batches}")

            # Combine all batch JSON files
            combined_questions = await self._combine_json_files(batch_json_files, document_id, task_id)

            # Enhancement 1: Upload combined JSON to S3
            if combined_questions:
                await self._upload_combined_json_to_s3(combined_questions, document_id, task_id)

            # Clean up batch JSON files
            for batch_file in batch_json_files:
                try:
                    os.unlink(batch_file)
                except:
                    pass

            # Clean up batch directory
            try:
                os.rmdir(json_output_dir)
            except:
                pass

            return combined_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in question parsing batches: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _call_llm_for_batch(self, text_content, start_question, end_question, task_id):
        """Call LLM to parse questions for a specific batch."""
        try:
            # Initialize LLM if needed
            self._initialize_llm()

            # Create the prompt using qp_parser_prompt
            prompt = qp_parser_prompt(start_question, end_question, text_content)

            # Create message
            message = HumanMessage(content=prompt)

            logger.info(f"[TASK:{task_id}] Calling LLM for questions {start_question}-{end_question}")

            try:
                response = await self.llm.ainvoke([message])
                response_content = response.content.strip()

                # Parse JSON response
                json_response = json.loads(response_content)

                logger.info(f"[TASK:{task_id}] LLM call successful for questions {start_question}-{end_question}")
                return json_response

            except json.JSONDecodeError as e:
                logger.warning(f"[TASK:{task_id}] JSON decode error : {e}")

            except Exception as e:
                logger.warning(f"[TASK:{task_id}] LLM call failed on: {e}")

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error calling LLM for batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _combine_json_files(self, batch_json_files, document_id, task_id):
        """Combine all batch JSON files into one list of questions."""
        try:
            combined_questions = []

            for json_file_path in batch_json_files:
                try:
                    with open(json_file_path, "r", encoding="utf-8") as f:
                        batch_data = json.load(f)

                    # Extract questions from the batch
                    questions = batch_data.get("questions", [])
                    if questions:
                        combined_questions.extend(questions)
                        logger.info(f"[TASK:{task_id}] Added {len(questions)} questions from {os.path.basename(json_file_path)}")
                    else:
                        logger.warning(f"[TASK:{task_id}] No questions found in {os.path.basename(json_file_path)}")

                except Exception as e:
                    logger.error(f"[TASK:{task_id}] Error reading batch file {json_file_path}: {e}")

            logger.info(f"[TASK:{task_id}] Combined {len(combined_questions)} questions from {len(batch_json_files)} batch files")
            return combined_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error combining JSON files: {e}")
            logger.error(traceback.format_exc())
            return []

    async def _upload_combined_json_to_s3(self, combined_questions, document_id, task_id):
        """Upload combined JSON questions to S3."""
        try:
            # Create combined JSON structure
            combined_json = {
                "questions": combined_questions,
                "total_questions": len(combined_questions),
                "document_id": document_id,
                "extraction_timestamp": str(uuid.uuid4())
            }

            # Create temporary file for JSON
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_file:
                json.dump(combined_json, temp_file, indent=2, ensure_ascii=False)
                temp_json_path = temp_file.name

            try:
                # Upload JSON file to S3 (same pattern as text file upload)
                s3_upload_result = upload_file_to_s3(
                    local_file_path=temp_json_path,
                    book_id="documents",
                    chapter_id=str(document_id),
                    res_id="main",
                    file_name=f"{document_id}_questions.json",
                    is_quiz_image=False
                )

                if s3_upload_result:
                    logger.info(f"[TASK:{task_id}] Successfully uploaded combined JSON to S3: {s3_upload_result}")
                else:
                    logger.error(f"[TASK:{task_id}] Failed to upload combined JSON to S3")

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_json_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error uploading combined JSON to S3: {e}")
            logger.error(traceback.format_exc())

    async def _save_questions_to_database(self, questions, document_id, username, task_id):
        """Save extracted questions to the database."""
        try:
            saved_count = 0

            for question_data in questions:
                try:
                    # Prepare solution data for database
                    solution_data = {
                        "exam_dtl_id": document_id,
                        "question": question_data.get("question", ""),
                        "question_type": question_data.get("question_type", "MCQ"),
                        "option1": question_data.get("option1", ""),
                        "option2": question_data.get("option2", ""),
                        "option3": question_data.get("option3", ""),
                        "option4": question_data.get("option4", ""),
                        "option5": question_data.get("option5", ""),
                        "answer": "",  # No answer in question paper
                        "marks": self._parse_numeric_field(question_data.get("marks")),
                        "negative_mark": self._parse_numeric_field(question_data.get("negative_mark")),
                        "topic": "",
                        "subtopic": "",
                        # Enhancement 3: Include directions if present
                        "directions": question_data.get("directions", "")
                    }

                    # Create the solution in database
                    solution_id = create_exam_solution(solution_data, username)
                    if solution_id:
                        saved_count += 1
                        logger.debug(f"[TASK:{task_id}] Saved question {question_data.get('question_number', 'unknown')} with ID {solution_id}")
                    else:
                        logger.warning(f"[TASK:{task_id}] Failed to save question {question_data.get('question_number', 'unknown')}")

                except Exception as e:
                    logger.error(f"[TASK:{task_id}] Error saving individual question: {e}")

            logger.info(f"[TASK:{task_id}] Successfully saved {saved_count} out of {len(questions)} questions to database")
            return saved_count

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error saving questions to database: {e}")
            logger.error(traceback.format_exc())
            return 0

    def _parse_numeric_field(self, value):
        """Parse a numeric field, handling various input types."""
        if value is None or value == "":
            return None

        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    async def _cleanup_local_folder(self, document_id, task_id):
        """Clean up the local folder for the document."""
        try:
            import shutil

            # Path to the local folder: agents/local_page_images/documents/{document_id}
            local_folder_path = os.path.join("agents", "local_page_images", "documents", str(document_id))

            if os.path.exists(local_folder_path):
                shutil.rmtree(local_folder_path)
                logger.info(f"[TASK:{task_id}] Successfully deleted local folder: {local_folder_path}")
            else:
                logger.debug(f"[TASK:{task_id}] Local folder does not exist: {local_folder_path}")

        except Exception as e:
            logger.warning(f"[TASK:{task_id}] Error cleaning up local folder: {e}")

    def _extract_quiz_images(self, res_id: str, chapter_id: str, book_id: str, image_urls, task_id: str, explanation_start_page: int = 5):
        """
        Extract quiz images from PDF pages and return the list of extracted image URLs.

        Args:
            res_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            image_urls: List of page image URLs
            task_id: Task ID for logging
            explanation_start_page: Page number from which explanations start (default: 5)

        Returns:
            List[str]: List of extracted quiz image URLs
        """
        try:
            img_extractor = ExtractorAgent()
            all_extracted_img_urls = []
            start_idx = 0

            for idx in range(start_idx, len(image_urls)):
                page_img_url = image_urls[idx]
                page_name = page_img_url.split('/')[-1]
                page_id = page_name.split('.')[0]

                mcq_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(res_id))

                # Check if the directory exists
                if not os.path.exists(mcq_full_img_storage_path):
                    logger.warning(f"[TASK:{task_id}] MCQ image storage path does not exist: {mcq_full_img_storage_path}")
                    continue

                mcq_local_image_paths = [
                    os.path.join(mcq_full_img_storage_path, img)
                    for img in os.listdir(mcq_full_img_storage_path)
                    if img.lower().endswith(f"{page_id}.png")
                ]
                mcq_local_image_paths.sort(key=lambda x: self._natural_sort_key(os.path.basename(x)))
                logger.debug(f"[TASK:{task_id}] Found {len(mcq_local_image_paths)} local image paths for page {page_id}")

                if mcq_local_image_paths:
                    logger.info(f"[TASK:{task_id}] Extracting mcq images for page {page_id}...")

                    prompt = pre_process_prompt
                    prompt_content = img_extractor.construct_input_content(prompt, mcq_local_image_paths)

                    # Ensure LLM is initialized before use
                    self._initialize_llm()
                    response = self.llm.invoke([HumanMessage(content=prompt_content, additional_kwargs={"tool_choice": "vision"})])

                    try:
                        # Try JSON load first
                        parsed = json.loads(response.content)
                    except json.JSONDecodeError:
                        # If it fails, try ast.literal_eval
                        parsed = ast.literal_eval(response.content)

                    mappings = parsed.get("mappings", [])
                    explanation_mappings = []

                    extracted_img_urls = img_extractor.extract_quiz_images_new(mcq_local_image_paths, book_id, chapter_id, res_id, mappings, explanation_mappings)
                    if extracted_img_urls:
                        all_extracted_img_urls.extend(extracted_img_urls)
                        logger.info(f"[TASK:{task_id}] Extracted {len(extracted_img_urls)} images for page {page_id}")

            logger.info(f"[TASK:{task_id}] Total extracted quiz images: {len(all_extracted_img_urls)}")
            return all_extracted_img_urls

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error extracting quiz images: {e}")
            logger.error(traceback.format_exc())
            return []

    def _natural_sort_key(self, filename):
        """
        Generate a natural sort key for filenames containing numbers.
        This ensures proper ordering like: file1.png, file2.png, file10.png
        """
        import re
        return [int(text) if text.isdigit() else text.lower() for text in re.split(r'(\d+)', filename)]

    def _append_images_to_questions(self, questions, images_from_content, task_id):
        """
        Simple approach: Append image URLs to question text and options in HTML format.
        Based on Grails appendImgLinks method.

        Args:
            questions: List of question dictionaries
            images_from_content: List of extracted image URLs
            task_id: Task ID for logging

        Returns:
            List[Dict]: Updated questions with images appended to text
        """
        try:
            logger.info(f"[TASK:{task_id}] Starting simple image appending for {len(questions)} questions and {len(images_from_content)} images")

            # Build CDN path based on config.CURRENT_ENV
            current_env = getattr(config, 'CURRENT_ENV', 'qa').lower()
            if current_env == 'publish':
                current_env = 'live'
            cdn_path = f"https://d1xcofdbxwssh7.cloudfront.net/{current_env}/supload/"

            # Create a simple mapping based on question numbers from filenames
            question_images = {}
            option_images = {}

            for image_url in images_from_content:
                try:
                    filename = os.path.basename(image_url)

                    if filename.startswith('question_'):
                        # Extract question number from filename
                        name_parts = filename.replace('.png', '').split('_')
                        if len(name_parts) >= 2:
                            try:
                                question_num = int(name_parts[1])

                                if len(name_parts) == 2:
                                    # question_X.png - question image
                                    if question_num not in question_images:
                                        question_images[question_num] = []
                                    question_images[question_num].append(image_url)

                                elif len(name_parts) >= 3 and name_parts[2] == 'option':
                                    # question_X_option_Y.png - option image
                                    if question_num not in option_images:
                                        option_images[question_num] = []
                                    option_images[question_num].append(image_url)

                            except ValueError:
                                logger.debug(f"Could not parse question number from {filename}")
                                continue

                except Exception as e:
                    logger.error(f"Error processing image {image_url}: {e}")
                    continue

            # Append images to questions
            updated_questions = []
            for question in questions:
                try:
                    question_number = question.get('question_number')
                    if question_number is not None:
                        try:
                            q_num = int(question_number)

                            # Append question images to question text
                            if q_num in question_images:
                                question_text = question.get('question', '')
                                question_text = self._append_img_links(question_text, question_images[q_num], cdn_path)
                                question['question'] = question_text
                                logger.debug(f"Appended {len(question_images[q_num])} images to question {q_num}")

                            # Append option images to respective options
                            if q_num in option_images:
                                # For simplicity, append to first option
                                option1 = question.get('op1', '')
                                if option1:
                                    question['op1'] = self._append_img_links(option1, option_images[q_num], cdn_path)

                        except (ValueError, TypeError):
                            logger.debug(f"Invalid question number format: {question_number}")

                    updated_questions.append(question)

                except Exception as e:
                    logger.error(f"Error appending images to question: {e}")
                    updated_questions.append(question)
                    continue

            logger.info(f"[TASK:{task_id}] Simple image appending completed for {len(updated_questions)} questions")
            return updated_questions

        except Exception as e:
            logger.error(f"[TASK:{task_id}] Error in simple image appending: {e}")
            logger.error(traceback.format_exc())
            # Return original questions without images on error
            return questions

    def _append_img_links(self, text, images_list, cdn_path):
        """
        Append image links to text in HTML format.
        Based on Grails appendImgLinks method.

        Args:
            text: Original text
            images_list: List of image URLs
            cdn_path: CDN base path

        Returns:
            str: Text with appended image HTML
        """
        if len(images_list) > 0:
            for image_url in images_list:
                # Extract the path after 'source=' parameter
                if 'source=' in image_url:
                    extracted_url = image_url.split('source=', 1)[1]
                else:
                    # If no 'source=' parameter, use the full URL path after domain
                    extracted_url = image_url.replace('https://', '').split('/', 1)[1] if '/' in image_url else image_url

                text += f"\n<p><img src='{cdn_path}{extracted_url}' /></p>"

        return text
